import { useState, useRef, useEffect, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vs } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { FileChangeManager } from './FileChangeManager';
import type { AIConversationState, AIMessage, ToolCall, WebviewMessage, ContentPart } from '../types';

interface AIChatProps {
    onSendMessage: (message: WebviewMessage) => void;
}

export const AIChat: React.FC<AIChatProps> = ({ onSendMessage }) => {
    const [inputValue, setInputValue] = useState('');
    const [state, setState] = useState<AIConversationState>({
        messages: [],
        isLoading: false,
    });

    // 当前正在处理的消息和工具调用
    const [currentMessage, setCurrentMessage] = useState<{
        id: string;
        content: string;
        toolCalls: ToolCall[];
        contentParts: ContentPart[];
    } | null>(null);
    
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const messagesEndRef = useRef<HTMLDivElement>(null);

    // 使用 ref 来保存最新的 currentMessage 值，避免闭包陷阱
    const currentMessageRef = useRef(currentMessage);
    currentMessageRef.current = currentMessage;

    // 管理工具调用的展开状态
    const [expandedToolCalls, setExpandedToolCalls] = useState<Set<string>>(new Set());

    // 处理来自后端的消息
    useEffect(() => {
        const handleMessage = (event: MessageEvent) => {
            const message = event.data;
            console.log('🔍 AI Chat: Received message:', message.type, message);

            switch (message.type) {
                case 'aiMessageStart':
                    setState(prev => ({
                        ...prev,
                        isLoading: true,
                        error: undefined,
                    }));
                    // 初始化当前消息
                    setCurrentMessage({
                        id: message.messageId,
                        content: '',
                        toolCalls: [],
                        contentParts: []
                    });
                    break;

                case 'aiMessageChunk':
                    console.log('Received chunk:', message.chunk);
                    // 更新当前消息内容
                    setCurrentMessage(prev => {
                        if (!prev) return null;
                        const newContent = prev.content + message.chunk;
                        const newContentParts = [...prev.contentParts];

                        // 如果最后一个部分是文本，则追加到它；否则创建新的文本部分
                        if (newContentParts.length > 0 && newContentParts[newContentParts.length - 1].type === 'text') {
                            newContentParts[newContentParts.length - 1].content += message.chunk;
                        } else {
                            newContentParts.push({
                                type: 'text',
                                content: message.chunk
                            });
                        }

                        const updated = {
                            ...prev,
                            content: newContent,
                            contentParts: newContentParts
                        };
                        console.log('Updated currentMessage:', updated);
                        return updated;
                    });
                    break;

                case 'aiToolCallStart':
                    console.log('🔧 Tool call started:', message);
                    // 添加新的工具调用
                    setCurrentMessage(prev => {
                        if (!prev) {
                            console.log('❌ No current message to add tool call to');
                            return null;
                        }

                        const newToolCalls = [...prev.toolCalls, message.toolCall];
                        const newContentParts = [...prev.contentParts, {
                            type: 'tool' as const,
                            content: '',
                            toolCall: message.toolCall
                        }];

                        return {
                            ...prev,
                            toolCalls: newToolCalls,
                            contentParts: newContentParts
                        };
                    });

                    // 模拟用户确认工具调用
                    console.log('🤖 Sending acceptToolCall to backend:', message.toolCall);
                    onSendMessage({
                        type: 'acceptToolCall',
                        message: message.id,
                    });

                    break;

                case 'aiToolCallComplete':
                    console.log('🏁 Tool call completed:', message.toolCall.name, message.toolCall.status);
                    // 更新工具调用结果
                    setCurrentMessage(prev => {
                        if (!prev) {
                            console.log('❌ No current message to update tool call result');
                            return null;
                        }

                        const updatedToolCalls = prev.toolCalls.map(tc =>
                            tc.id === message.toolCall.id ? message.toolCall : tc
                        );

                        const updatedContentParts = prev.contentParts.map(part =>
                            part.type === 'tool' && part.toolCall?.id === message.toolCall.id
                                ? { ...part, toolCall: message.toolCall }
                                : part
                        );

                        return {
                            ...prev,
                            toolCalls: updatedToolCalls,
                            contentParts: updatedContentParts
                        };
                    });
                    break;

                case 'aiMessageComplete': {
                    console.log('🏁 Message complete received');

                    // 确保 timestamp 是 Date 对象，并合并工具调用和内容部分
                    const aiMessage = {
                        ...message.message,
                        timestamp: new Date(message.message.timestamp),
                        // 使用 currentMessage 中的工具调用，因为它们是流式更新的
                        toolCalls: currentMessageRef.current?.toolCalls || message.message.toolCalls || [],
                        // 保留 contentParts 以维持内容和工具调用的顺序
                        contentParts: currentMessageRef.current?.contentParts || []
                    };

                    console.log('✅ [FIXED] Final aiMessage with contentParts:', {
                        hasContentParts: !!aiMessage.contentParts?.length,
                        contentPartsCount: aiMessage.contentParts?.length || 0,
                        toolCallsCount: aiMessage.toolCalls?.length || 0
                    });

                    setState(prev => ({
                        ...prev,
                        messages: [...prev.messages, aiMessage],
                        isLoading: false,
                    }));

                    // 检查是否还有待处理的工具调用
                    const hasPendingToolCalls = currentMessageRef.current?.toolCalls?.some(tc => tc.status === 'pending');

                    if (!hasPendingToolCalls) {
                        // 清除当前消息
                        setCurrentMessage(null);
                    }
                    break;
                } // End of aiMessageComplete case

                case 'aiError':
                    setState(prev => ({
                        ...prev,
                        error: message.error,
                        isLoading: false,
                    }));
                    // 清除当前消息
                    setCurrentMessage(null);
                    break;

                default:
                    // 忽略其他消息类型
                    break;
            }
        };

        window.addEventListener('message', handleMessage);
        return () => {
            window.removeEventListener('message', handleMessage);
        };
    }, []); // 现在可以安全地使用空依赖数组，因为我们使用了 ref

    // 自动调整输入框高度
    const adjustTextareaHeight = () => {
        const textarea = textareaRef.current;
        if (textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
        }
    };

    useEffect(() => {
        adjustTextareaHeight();
    }, [inputValue]);

    // 滚动到底部
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [state.messages]);

    // 当流式消息更新时也要滚动到底部
    useEffect(() => {
        if (currentMessage && currentMessage.content) {
            // 使用 requestAnimationFrame 确保 DOM 更新后再滚动
            requestAnimationFrame(() => {
                scrollToBottom();
            });
        }
    }, [currentMessage]);



    // 处理消息发送
    const handleSendMessage = () => {
        if (!inputValue.trim() || state.isLoading) return;

        const userMessage: AIMessage = {
            id: Date.now().toString(),
            role: 'user',
            content: inputValue.trim(),
            timestamp: new Date(),
        };

        setState(prev => ({
            ...prev,
            messages: [...prev.messages, userMessage],
            isLoading: true,
        }));

        // 发送消息到后端
        onSendMessage({
            type: 'sendAIMessage',
            message: inputValue.trim(),
        });

        setInputValue('');
    };

    // 处理键盘事件
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter') {
            if (e.shiftKey) {
                // Shift+Enter 换行，不做任何处理，让默认行为发生
                return;
            } else {
                // Enter 发送消息
                e.preventDefault();
                handleSendMessage();
            }
        }
    };

    // 优化的Markdown渲染组件
    const MarkdownRenderer = useMemo(() => {
        return ({ content }: { content: string }) => (
            <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                    code({ node, className, children, ...props }: any) {
                        const match = /language-(\w+)/.exec(className || '');
                        const inline = !match;
                        return !inline && match ? (
                            <SyntaxHighlighter
                                style={vs}
                                language={match[1]}
                                PreTag="div"
                                {...props}
                            >
                                {String(children).replace(/\n$/, '')}
                            </SyntaxHighlighter>
                        ) : (
                            <code className={className} {...props}>
                                {children}
                            </code>
                        );
                    },
                }}
            >
                {content}
            </ReactMarkdown>
        );
    }, []);

    // 截断文本函数
    const truncateText = (text: string, maxLength: number = 100) => {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    };

    // 切换工具调用展开状态
    const toggleToolCallExpanded = (toolCallId: string) => {
        setExpandedToolCalls(prev => {
            const newSet = new Set(prev);
            if (newSet.has(toolCallId)) {
                newSet.delete(toolCallId);
            } else {
                newSet.add(toolCallId);
            }
            return newSet;
        });
    };

    // 渲染工具调用 - 支持展开/收缩
    const renderToolCall = (toolCall: ToolCall) => {
        console.log('🎨 Rendering tool call:', toolCall);

        const isExpanded = expandedToolCalls.has(toolCall.id);
        const parametersText = JSON.stringify(toolCall.parameters, null, 2);
        const truncatedParameters = truncateText(parametersText, 10000);
        const truncatedResult = toolCall.result ? truncateText(toolCall.result, 10000) : '';

        return (
            <div key={toolCall.id} className="tool-call">
                <div
                    className="tool-call-header clickable"
                    onClick={() => toggleToolCallExpanded(toolCall.id)}
                >
                    <span className="tool-name">🔧 {toolCall.name}</span>
                    <div className="tool-header-right">
                        <span className={`tool-status ${toolCall.status}`}>
                            {toolCall.status === 'pending' && '⏳ 执行中'}
                            {toolCall.status === 'success' && '✅ 成功'}
                            {toolCall.status === 'error' && '❌ 失败'}
                        </span>
                        <span className="expand-icon">
                            {isExpanded ? '▼' : '▶'}
                        </span>
                    </div>
                </div>
                {isExpanded && (
                    <>
                        <div className="tool-parameters">
                            <strong>参数:</strong>
                            <div className="tool-content">
                                <code>{truncatedParameters}</code>
                            </div>
                        </div>
                        {toolCall.result && (
                            <div className="tool-result">
                                <strong>结果:</strong>
                                <div className="tool-content">
                                    <code>{truncatedResult}</code>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>
        );
    };

    // 渲染消息
    const renderMessage = (message: AIMessage) => {
        // 确保 timestamp 是 Date 对象
        const timestamp = message.timestamp instanceof Date ?
            message.timestamp : new Date(message.timestamp);

        return (
            <div key={message.id} className={`message ${message.role}`}>
                <div className="message-header">
                    <span className="message-role">
                        {message.role === 'user' ? '👤 用户' : '🤖 AI助手'}
                    </span>
                    <span className="message-time">
                        {timestamp.toLocaleTimeString()}
                    </span>
                </div>
                <div className="message-content">
                    {message.role === 'assistant' ? (
                        // 如果有 contentParts，使用它来保持顺序；否则回退到传统渲染
                        message.contentParts && message.contentParts.length > 0 ? (
                            <div className="content-parts">
                                {message.contentParts.map((part, index) => (
                                    <div key={index}>
                                        {part.type === 'text' ? (
                                            <MarkdownRenderer content={part.content} />
                                        ) : (
                                            part.toolCall ? (
                                                <>
                                                    {console.log('🔧 [+] About to render tool call:', part.toolCall)}
                                                    {renderToolCall(part.toolCall)}
                                                </>
                                            ) : (
                                                <>
                                                    {console.log('❌ [+] Tool call is null for tool part')}
                                                    <div>Tool call data missing</div>
                                                </>
                                            )
                                        )}
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <>
                                {console.log('⚠️ [FALLBACK] Using fallback rendering for message:', message.id, 'contentParts:', message.contentParts?.length || 0)}
                                <MarkdownRenderer content={message.content} />
                                {message.toolCalls && message.toolCalls.length > 0 && (
                                    <div className="tool-calls">
                                        {message.toolCalls.map(renderToolCall)}
                                    </div>
                                )}
                            </>
                        )
                    ) : (
                        <div className="user-message">{message.content}</div>
                    )}
                </div>
            </div>
        );
    };

    return (
        <div className="ai-chat">
            <div className="chat-header">
                <h3>AI 对话助手</h3>
                <div className="chat-status">
                    {state.isLoading && <span className="loading-indicator">思考中...</span>}
                    {state.error && <span className="error-indicator">错误: {state.error}</span>}
                </div>
            </div>
            
            <div className="messages-container">
                {state.messages.length === 0 ? (
                    <div className="empty-chat">
                        <div className="welcome-message">
                            <h4>👋 欢迎使用AI对话助手</h4>
                            <p>您可以向我提问任何问题，我会尽力为您解答。</p>
                            <p>💡 提示：使用 <kbd>Shift + Enter</kbd> 换行，<kbd>Enter</kbd> 发送消息</p>
                        </div>
                    </div>
                ) : (
                    <>
                        {state.messages.map(renderMessage)}
                        {/* 显示当前正在处理的消息 */}
                        {currentMessage && (
                            <div className="message assistant current-message">
                                <div className="message-header">
                                    <span className="message-role">🤖 AI助手</span>
                                    <span className="message-time">正在回复...</span>
                                </div>
                                <div className="message-content">
                                    {currentMessage.contentParts.length > 0 ? (
                                        <div className="streaming-content">
                                            {currentMessage.contentParts.map((part, index) => {
                                                console.log(`🔍 Rendering part ${index}:`, part);
                                                return (
                                                    <div key={index}>
                                                        {part.type === 'text' ? (
                                                            <MarkdownRenderer content={part.content} />
                                                        ) : (
                                                            part.toolCall ? (
                                                                <>
                                                                    {console.log('🔧 About to render tool call:', part.toolCall)}
                                                                    {renderToolCall(part.toolCall)}
                                                                </>
                                                            ) : (
                                                                <>
                                                                    {console.log('❌ Tool call is null for tool part')}
                                                                    <div>Tool call data missing</div>
                                                                </>
                                                            )
                                                        )}
                                                    </div>
                                                );
                                            })}
                                            {/* 只在有文本内容时显示光标 */}
                                            {currentMessage.content && (
                                                <span className="typing-cursor">|</span>
                                            )}
                                        </div>
                                    ) : (
                                        <div className="thinking-indicator">
                                            <span>🤔 正在思考...</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}
                    </>
                )}
                <div ref={messagesEndRef} />
            </div>

            <FileChangeManager onSendMessage={onSendMessage} />

            <div className="input-container">
                <div className="input-wrapper">
                    <textarea
                        ref={textareaRef}
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyDown={handleKeyDown}
                        placeholder="输入您的问题... (Shift+Enter 换行，Enter 发送)"
                        disabled={state.isLoading}
                        className="message-input"
                        rows={1}
                    />
                    <div className="send-button-container">
                        <button
                            onClick={handleSendMessage}
                            disabled={!inputValue.trim() || state.isLoading}
                            className="send-button"
                            title={state.isLoading ? '发送中...' : '发送消息'}
                        >
                            {state.isLoading ? '⏳' : '➤'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};
