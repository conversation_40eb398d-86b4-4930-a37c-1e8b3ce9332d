import { tool } from "@langchain/core/tools";
import { promises as fs } from "fs";
import { dirname } from "path";
import { z } from "zod";
import { getSnapshotService } from "../../../snapshot/SnapshotService";
import { notifyFileModified } from "../../../notification/NotificationService";

/**
 * 创建新文件的工具
 * 如果目录不存在，会自动创建目录
 * 不能用于编辑已存在的文件
 */
export const createFile = tool(
  async (input: { filePath: string; content: string }, config: any) => {
    console.log("Creating file:", input.filePath);

    try {
      let snapshotId: string | null = null;
      let fileExists = false;

      // 检查文件是否已存在
      try {
        await fs.access(input.filePath);
        fileExists = true;

        // 文件已存在，创建快照
        if (config?.configurable?.thread_id) {
          try {
            const snapshotService = getSnapshotService();
            snapshotService.setCurrentThreadId(config.configurable.thread_id);
            const snapshot = await snapshotService.createSnapshotBeforeModification(
              input.filePath,
              `Before file overwrite by createFile`
            );
            snapshotId = snapshot?.id || null;
            console.log(`Created snapshot for existing file: ${input.filePath}`);
          } catch (snapshotError) {
            console.warn("Failed to create snapshot for existing file:", snapshotError);
          }
        }

        console.log(`File exists, will overwrite: ${input.filePath}`);
      } catch (error: any) {
        // 如果文件不存在，继续创建
        if (error.code !== "ENOENT") {
          throw error;
        }
        console.log(`Creating new file: ${input.filePath}`);
      }

      // 确保目录存在
      const dir = dirname(input.filePath);
      await fs.mkdir(dir, { recursive: true });

      // 创建文件
      await fs.writeFile(input.filePath, input.content, "utf8");

      // 如果覆盖了现有文件且有快照，通知前端
      if (fileExists && snapshotId) {
        try {
          await notifyFileModified(input.filePath, snapshotId);
        } catch (notifyError) {
          console.warn("Failed to notify file modification:", notifyError);
        }
      }

      const message = fileExists
        ? `File overwritten successfully at: ${input.filePath}`
        : `File created successfully at: ${input.filePath}`;
      console.log(message);
      return message;
    } catch (error: any) {
      console.error("Error creating file:", error.message);
      throw new Error(`Failed to create file: ${error.message}`);
    }
  },
  {
    name: "create_file",
    schema: z.object({
      filePath: z.string().describe("The absolute path to the file to create."),
      content: z.string().describe("The content to write to the file."),
    }),
    description:
      "This is a tool for creating a new file in the workspace. The file will be created with the specified content. The directory will be created if it does not already exist. Never use this tool to edit a file that already exists.",
  }
);

/**
 * 创建目录结构的工具
 * 递归创建所有目录，类似于 mkdir -p
 * 注意：在使用 create_file 工具之前不需要使用此工具，create_file 会自动创建所需的目录
 */
export const createDirectory = tool(
  async (input: { dirPath: string }) => {
    console.log("Creating directory:", input.dirPath);

    try {
      // 递归创建目录结构
      await fs.mkdir(input.dirPath, { recursive: true });

      console.log("Directory created successfully:", input.dirPath);
      return `Directory created successfully at: ${input.dirPath}`;
    } catch (error: any) {
      console.error("Error creating directory:", error.message);
      throw new Error(`Failed to create directory: ${error.message}`);
    }
  },
  {
    name: "create_directory",
    schema: z.object({
      dirPath: z
        .string()
        .describe("The absolute path to the directory to create."),
    }),
    description:
      "Create a new directory structure in the workspace. Will recursively create all directories in the path, like mkdir -p. You do not need to use this tool before using create_file, that tool will automatically create the needed directories.",
  }
);
