import { MultiServerMCPClient } from "@langchain/mcp-adapters";


// const client = new MultiServerMCPClient({
//   mcpServers: {
    // "mysql": {
    //   "command": "uv",
    //   "args": [
    //     "--directory",
    //     "/Users/<USER>/miniconda3/lib/python3.12/site-packages/mysql_mcp_server",
    //     "run",
    //     "mysql_mcp_server"
    //   ],
    //   "env": {
    //     "MYSQL_HOST": "localhost",
    //     "MYSQL_PORT": "3306",
    //     "MYSQL_USER": "root",
    //     "MYSQL_PASSWORD": "your_password",
    //     "MYSQL_DATABASE": "test"
    //   }
    // },

    // "bing-cn": {
    //   "command": "npx",
    //   "args": [
    //     "bing-cn-mcp"
    //   ],
    //   "env": {
    //     "USER_AGENT": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    //   }
    // },

    // "pdf-tools": {
    //   "command": "npx",
    //   "args": [
    //     "@mcp-apps/pdf-tools-mcp-server"
    //   ]
    // },

    // "12306": {
    //   "command": "npx",
    //   "args": [
    //     "12306-mcp"
    //   ]
    // },

    // "weather": {
    //   // Ensure your start your weather server on port 8000
    //   url: "http://localhost:8000/sse",
    //   transport: "sse",
    // }
//   }
// })

// export default client;