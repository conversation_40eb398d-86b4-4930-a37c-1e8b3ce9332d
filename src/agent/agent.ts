import { AIMessage, SystemMessage } from "@langchain/core/messages";
import { MessagesAnnotation, StateGraph } from "@langchain/langgraph";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import "dotenv/config";
// import client from "./mcpClient.js";
import { modelUsed } from "./models.js";
import { addHumanInTheLoopToTools, allTools } from "./tools/index.js";
import { create_system_prompt } from "./systemPrompt.js";


export async function createCustomAgent(checkpointer: any) {

  // const tools = (await client.getTools())
  // .filter(tool => tool.name !== 'mcp__12306__get-current-date');
  // console.log("🛠️ Available tools:", tools.map((tool: any) => tool.name));

  // 获取原始工具
  const originalTools = [
    ...allTools,
    // ...tools,
  ];

  const toolsWithCustomConfirmation = addHumanInTheLoopToTools(originalTools, {
    defaultDescription: "请确认工具调用",
    toolConfigs: {},
  });

  const bindTools = toolsWithCustomConfirmation

  const model = modelUsed.bindTools(bindTools, { parallel_tool_calls: false });

  const shouldContinue = async (state: typeof MessagesAnnotation.State) => {
    const messages = state.messages;
    const lastMessage: AIMessage = messages[messages.length - 1];
    // If the LLM makes a tool call, then we route to the "tools" node
    if (lastMessage.tool_calls?.length) {
      return "tools";
    }
    // Otherwise, we stop (reply to the user)
    return "final";
  };

  const callModel = async (state: typeof MessagesAnnotation.State) => {
    const messages = state.messages.filter((msg: any) => msg.constructor.name !== 'ChatMessageChunk');

    const systemMessage = new SystemMessage(await create_system_prompt());

    // 将系统消息添加到消息列表的开头（如果还没有系统消息的话）
    const hasSystemMessage = messages.some(
      (msg: any) => msg instanceof SystemMessage
    );
    const messagesWithSystem = hasSystemMessage
      ? messages
      : [systemMessage, ...messages];
      // : [...messages];

    const response = await model.invoke(messagesWithSystem);
    // We return a list, because this will get added to the existing list
    return { messages: [response] };
  };

  // Agent开始节点 - 在stream中增加开始消息
  const startNode = async (state: typeof MessagesAnnotation.State) => {
    console.log("🚀 Agent开始执行...");

    // 返回最后一条消息
    const messages = state.messages;
    return { messages: [messages[messages.length - 1]] };
  };

  // Agent结束节点 - 在stream中增加结束消息
  const finalNode = async (state: typeof MessagesAnnotation.State) => {
    console.log("✅ Agent执行完成");

    // 返回最后一条消息
    const messages = state.messages;
    return { messages: [messages[messages.length - 1]] };
  };

  const toolNode = new ToolNode<typeof MessagesAnnotation.State>(
    bindTools
  );

  const graph = new StateGraph(MessagesAnnotation)
    // 添加所有节点
    .addNode("start", startNode) // Agent开始节点
    .addNode("agent", callModel)
    .addNode("tools", toolNode)
    .addNode("final", finalNode)
    // 设置边连接
    .addEdge("__start__", "start") // 从开始到Agent开始节点
    .addEdge("start", "agent") // 从Agent开始到主Agent节点
    // Third parameter is optional and only here to draw a diagram of the graph
    .addConditionalEdges("agent", shouldContinue, {
      tools: "tools",
      final: "final",
    })
    .addEdge("tools", "agent")
    .addEdge("final", "__end__") // 从Agent结束到结束
    .compile({
      checkpointer,
      // interruptBefore: ["tools"],
    });

  return graph;
}
