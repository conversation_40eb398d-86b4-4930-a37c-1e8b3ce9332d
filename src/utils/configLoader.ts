import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';
import * as os from 'os';

const ENV_EXAMPLE = `
MODEL_PROXY_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
MODEL_PROXY_API_KEY=your_api_key_here
MODEL_PROXY_MODEL=qwen3-coder-480b-a35b-instruct
`;

/**
 * Ensures the .yike directory and .env file exist in the specified directory.
 * Creates them with default content if they don't exist.
 * 
 * @param directoryPath The directory where .yike/.env should exist
 */
function ensureEnvFile(directoryPath: string): void {
    try {
        const yikeDir = path.join(directoryPath, '.yike');
        const envPath = path.join(yikeDir, '.env');

        // Create .yike directory if it doesn't exist
        if (!fs.existsSync(yikeDir)) {
            fs.mkdirSync(yikeDir, { recursive: true });
            console.log(`Created directory: ${yikeDir}`);
        }

        // Create .env file if it doesn't exist
        if (!fs.existsSync(envPath)) {
            fs.writeFileSync(envPath, ENV_EXAMPLE.trim());
            console.log(`Created .env file at: ${envPath}`);
        }
    } catch (error) {
        console.error(`Error ensuring .env file at ${directoryPath}:`, error);
    }
}

// Initialize environment files in both user home and workspace directories
function initializeEnvFiles(): void {
    try {
        // Initialize in user home directory
        ensureEnvFile(os.homedir());
        
        // Initialize in workspace directory if available
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            ensureEnvFile(workspaceFolders[0].uri.fsPath);
        }
    } catch (error) {
        console.error('Error initializing environment files:', error);
    }
}

// Call initialization when this module is loaded
initializeEnvFiles();

/**
 * Loads environment variables from .env files in both user home and workspace directories.
 * Workspace variables take precedence over user home variables.
 * 
 * @returns An object containing all loaded environment variables
 */
export function loadEnvVariables(): Record<string, string> {
    const envVars: Record<string, string> = {};
    
    // 1. Load from user home directory
    const userEnvPath = path.join(os.homedir(), '.yike', '.env');
    loadEnvFile(userEnvPath, envVars);
    
    // 2. Load from workspace directory (takes precedence)
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (workspaceFolders && workspaceFolders.length > 0) {
        const workspaceEnvPath = path.join(workspaceFolders[0].uri.fsPath, '.yike', '.env');
        loadEnvFile(workspaceEnvPath, envVars);
    }
    
    return envVars;
}

/**
 * Loads environment variables from a .env file and merges them into the provided object.
 * If the file doesn't exist, does nothing.
 * 
 * @param filePath Path to the .env file
 * @param envVars Object to merge the loaded variables into
 */
function loadEnvFile(filePath: string, envVars: Record<string, string>): void {
    try {
        if (fs.existsSync(filePath)) {
            const content = fs.readFileSync(filePath, 'utf-8');
            if (content.trim() === '') {
                console.warn(`Empty .env file at: ${filePath}`);
                return;
            }
            const envConfig = dotenv.parse(content);
            Object.assign(envVars, envConfig);
        }
    } catch (error) {
        console.error(`Error loading .env file at ${filePath}:`, error);
    }
}

/**
 * Gets the value of an environment variable, checking both process.env and loaded .env files.
 * 
 * @param key The environment variable key
 * @param defaultValue Default value if the variable is not found
 * @returns The value of the environment variable or the default value
 */
export function getEnvVariable(key: string, defaultValue: string = ''): string {
    // First check process.env (system environment variables)
    if (process.env[key]) {
        return process.env[key] as string;
    }
    
    // Then check .env files
    const envVars = loadEnvVariables();
    return envVars[key] || defaultValue;
}
