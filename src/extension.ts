import * as vscode from 'vscode';
import { YiKeViewProvider } from './yikeViewProvider';
import { loadEnvVariables } from './utils/configLoader';

// Global variable to store environment variables
export let envVariables: Record<string, string> = {};

export async function activate(context: vscode.ExtensionContext) {
    console.log('YiKe extension is now active!');

    // Load environment variables
    try {
        envVariables = await loadEnvVariables();
        console.log('Loaded environment variables:', Object.keys(envVariables).length > 0 ? envVariables : 'No environment variables found');
    } catch (error) {
        console.error('Failed to load environment variables:', error);
        vscode.window.showErrorMessage('Failed to load environment variables. Check the console for details.');
    }

    provider = new YiKeViewProvider(context.extensionUri, envVariables);

    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(
            YiKeViewProvider.viewType,
            provider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true
                }
            }
        )
    );
}

let provider: YiKeViewProvider | undefined;

export function deactivate() {
    if (provider) {
        provider.dispose();
    }
}