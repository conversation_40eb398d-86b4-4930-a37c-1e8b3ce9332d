import * as vscode from 'vscode';

/**
 * 通知服务
 * 用于向前端 webview 发送各种通知消息
 */
export class NotificationService {
    private static instance: NotificationService;
    private webviewView: vscode.WebviewView | null = null;

    private constructor() {}

    /**
     * 获取通知服务单例实例
     */
    static getInstance(): NotificationService {
        if (!NotificationService.instance) {
            NotificationService.instance = new NotificationService();
        }
        return NotificationService.instance;
    }

    /**
     * 设置 webview 实例
     */
    setWebviewView(webviewView: vscode.WebviewView): void {
        this.webviewView = webviewView;
        console.log('NotificationService: WebviewView set');
    }

    /**
     * 发送消息到前端
     */
    private sendMessage(message: any): void {
        if (this.webviewView) {
            this.webviewView.webview.postMessage(message);
            console.log('NotificationService: Message sent to webview:', message.type);
        } else {
            console.warn('NotificationService: WebviewView not available, message not sent:', message.type);
        }
    }

    /**
     * 通知文件已被修改
     */
    notifyFileModified(filePath: string, snapshotId: string): void {
        this.sendMessage({
            type: 'fileModified',
            filePath: filePath,
            snapshotId: snapshotId,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 通知快照已创建
     */
    notifySnapshotCreated(filePath: string, snapshotId: string): void {
        this.sendMessage({
            type: 'snapshotCreated',
            filePath: filePath,
            snapshotId: snapshotId,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 通知文件变更已被接受
     */
    notifyFileChangeAccepted(filePath: string): void {
        this.sendMessage({
            type: 'fileChangeAccepted',
            filePath: filePath,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 通知文件变更已被撤销
     */
    notifyFileChangeReverted(filePath: string): void {
        this.sendMessage({
            type: 'fileChangeReverted',
            filePath: filePath,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 通知工具调用开始
     */
    notifyToolCallStart(toolName: string, filePath?: string): void {
        this.sendMessage({
            type: 'toolCallStart',
            toolName: toolName,
            filePath: filePath,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 通知工具调用完成
     */
    notifyToolCallComplete(toolName: string, filePath?: string, success: boolean = true): void {
        this.sendMessage({
            type: 'toolCallComplete',
            toolName: toolName,
            filePath: filePath,
            success: success,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 清理资源
     */
    cleanup(): void {
        this.webviewView = null;
        console.log('NotificationService: Cleaned up');
    }
}

/**
 * 便捷函数：获取通知服务实例
 */
export function getNotificationService(): NotificationService {
    return NotificationService.getInstance();
}

/**
 * 便捷函数：通知文件已被修改
 */
export async function notifyFileModified(filePath: string, snapshotId: string): Promise<void> {
    const service = getNotificationService();
    service.notifyFileModified(filePath, snapshotId);
}

/**
 * 便捷函数：通知快照已创建
 */
export async function notifySnapshotCreated(filePath: string, snapshotId: string): Promise<void> {
    const service = getNotificationService();
    service.notifySnapshotCreated(filePath, snapshotId);
}
