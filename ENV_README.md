# 环境变量配置说明

本扩展支持从两个位置加载环境变量：

1. 用户主目录下的 `.yike/.env` 文件（`~/.yike/.env`）
2. 当前工作区目录下的 `.yike/.env` 文件（`${workspaceFolder}/.yike/.env`）

工作区目录下的配置会覆盖用户主目录下的同名配置。

## 配置文件格式

`.env` 文件使用标准的键值对格式，例如：

```env
API_KEY=your_api_key_here
API_SECRET=your_api_secret_here
DEBUG=true
```

## 在扩展中访问环境变量

在扩展代码中，可以通过以下方式访问环境变量：

```typescript
import { getEnvVariable } from './utils/configLoader';

// 获取环境变量，如果不存在则返回默认值
const apiKey = await getEnvVariable('API_KEY', 'default_value');
```

## 在 Webview 中访问环境变量

在 Webview 中，可以通过以下方式获取环境变量：

```typescript
// 请求环境变量
window.addEventListener('message', event => {
    if (event.data.command === 'envVariables') {
        const envVars = event.data.data;
        console.log('Environment variables:', envVars);
    }
});

// 发送获取环境变量的请求
if (vscode && vscode.postMessage) {
    vscode.postMessage({
        command: 'getEnvVariables'
    });
}
```

## 最佳实践

1. 敏感信息（如 API 密钥）应存储在用户主目录下的 `.env` 文件中
2. 项目特定的配置应存储在工作区目录的 `.env` 文件中
3. 不要将 `.env` 文件提交到版本控制系统中（确保将其添加到 `.gitignore` 中）
4. 提供默认值以增强代码的健壮性
