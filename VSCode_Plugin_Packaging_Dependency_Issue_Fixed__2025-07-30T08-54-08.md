[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:重构 agent.ts 中 createCustomAgent 创建模式 DESCRIPTION: 当前智能体创建方式非常基础，不利于扩展，因此需要进行重构；重构需求如下：
对于智能体构成要素：
1. 系统提示词
2. 工具
3. 模型
4. 人类介入配置
5. graph编排
6. checkpoint
分别改造为provider模式，便于扩展和替换. 首先为以上要素提供内置provider，将当前代码实现置于内置provider中。
后续通过提供不同provider的机制扩展，例如：新建智能体时，工具可以从某个配置文件加载，读取工具列表，该场景可以使用工具配置文件provider
-[ ] NAME:测试2 DESCRIPTION:描述2